"use client";
import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import ReservationForm from "./ReservationForm";
import ServiceForm from "./ServiceForm";
import SubscriptionForm from "./SubscriptionForm";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";

interface ReservationDialogProps {
  dialogOpen: boolean;
  activeSection: "reservation" | "service" | "subscription" | null;
  startDate: Date | string | undefined;
  endDate: Date | string | undefined;
  selectedPetTypes: string | null;
  selectedRooms: string | null;
  allocations: any[];
  loading: boolean;
  hotelToken: string | undefined;
  onDialogClose: () => void;
  onPetTypeChange: (value: string) => void;
  onRoomChange: (value: string) => void;
}

const ReservationDialog: React.FC<ReservationDialogProps> = ({
  dialogOpen,
  activeSection,
  startDate,
  endDate,
  selectedPetTypes,
  selectedRooms,
  allocations,
  loading,
  hotelToken,
  onDialogClose,
  onPetTypeChange,
  onRoomChange,
}) => {
  const { addItemToCart } = useHotelAtDoorReservation();
  const userReservationData = useSelector(
    (state: RootState) => state.userReservationData.userReservationData
  );
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const handleAddToCart = () => {
    switch (activeSection) {
      case "reservation":
        const requestBody = {
          itemType: "reservation",
          itemData: {
            room: selectedRooms,
            startDate: startDate,
            endDate: endDate,
            pet: userReservationData?.selectedPet,
          },
          selectedItems: calculatedRoomData?.selectedItems || [],
          totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
        };

        try {
          addItemToCart(hotelToken, requestBody);
          console.log("Ekleme işlemi başarılı:", requestBody);
          onDialogClose();
        } catch (error) {
          console.error("Ekleme işlemi başarısız:", error);
        }
        break;
      case "service":
        console.log("Hizmet için alan doldurma işlemi");
        onDialogClose();
        break;
      case "subscription":
        console.log("Üyelik kartı için alan doldurma işlemi");
        onDialogClose();
        break;
    }
  };

  const renderDialogContent = () => {
    switch (activeSection) {
      case "reservation":
        return (
          <ReservationForm
            startDate={startDate}
            endDate={endDate}
            selectedPetTypes={selectedPetTypes}
            selectedRooms={selectedRooms}
            allocations={allocations}
            loading={loading}
            onPetTypeChange={onPetTypeChange}
            onRoomChange={onRoomChange}
          />
        );
      case "service":
        return <ServiceForm />;
      case "subscription":
        return <SubscriptionForm />;
      default:
        return (
          <div className="text-neutral-500">Lütfen bir seçenek belirleyin.</div>
        );
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={onDialogClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {activeSection === "reservation" && "Konaklama"}
            {activeSection === "service" && "Hizmet"}
            {activeSection === "subscription" && "Üyelik Kartı"}
          </DialogTitle>
          <DialogDescription>
            Lütfen aşağıdaki alanlardan seçim yapınız.
          </DialogDescription>
        </DialogHeader>
        {renderDialogContent()}
        <DialogFooter>
          <Button variant="ghost" onClick={onDialogClose}>
            İptal
          </Button>
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={handleAddToCart}
          >
            Ekle
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReservationDialog;
