"use client";
import React, { useState } from "react";
import type { FC, ChangeEvent } from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import Textarea from "@/shared/Textarea";
import { format } from "date-fns";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import LoadingSpinner from "@/shared/icons/Spinner";
import { usePathname } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { createLocalDate } from "@/utils/createLocalDate";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import { datePickerLanguageHandler } from "@/utils/datePickerLanguageHandler";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";

interface VeterinaryServiceProps {
  service: any;
  hotelToken: string | undefined;
  hotelId: string;
  loading: boolean;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  closeModal: () => void;
  formData: any;
  setFormData: React.Dispatch<React.SetStateAction<any>>;
  petOwnerPetData?: any;
}

const VeterinaryService: FC<VeterinaryServiceProps> = ({
  service,
  hotelToken,
  hotelId,
  loading,
  setLoading,
  closeModal,
  formData,
  setFormData,
  petOwnerPetData,
}) => {
  const translate = useTranslations("ServicesCart");
  const pathname = usePathname();
  const { addItemToCart } = useHotelAtDoorReservation();
  const [selectedPet, setSelectedPet] = useState<string>("");
  const [disabled, setDisabled] = useState<boolean>(false);
  const today = format(new Date(), "yyyy-MM-dd");
  const calendarLanguage = datePickerLanguageHandler(translate("language"));
  const monthFormatter = new Intl.DateTimeFormat(
    calendarLanguage.firstValue === "tr" ? "tr" : "en",
    {
      month: "long",
    }
  );

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const handleChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormData((prevState: any) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const buttonDisabledHandler = () => {
    if (!selectedPet) {
      return { errorText: "", disabled: true };
    }

    if (formData.serviceDate.trim() === "") {
      return { errorText: "", disabled: true };
    }

    const isValidDate = new Date(formData.serviceDate) >= new Date(today);
    if (!isValidDate) {
      return {
        errorText: translate("errorText"),
        disabled: true,
      };
    }

    return { errorText: "", disabled: false };
  };

  const isButtonDisabled = buttonDisabledHandler();

  const addToCartHandler = () => {
    const requestBody = {
      itemType: "service",
      itemData: {
        ...service?.serviceData,
        note: formData.note,
        serviceDate: formData.serviceDate,
        serviceType: service?.serviceType,
        serviceHotelId: service?._id,
        hotel: hotelId,
        pet: "",
      },
      selectedItems: calculatedRoomData?.selectedItems || [],
      totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
    };

    addItemToCart(
      hotelToken,
      requestBody,
      setLoading,
      closeModal,
      setDisabled
    );
  };

  return (
    <div
      className="space-y-3"
    >
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">{translate("serviceName")}:</p>
          <p className="capitalize font-medium text-sm">
            {service?.serviceData?.serviceName}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">{translate("vetName")}:</p>
          <p className="text-sm capitalize font-medium">
            {service?.serviceData?.serviceDetails?.veterinarianName}
          </p>
        </div>
        <div className="max-sm:flex-col flex sm:items-center sm:gap-1">
          <p className="text-sm font-semibold">{translate("requiredFiles")}:</p>
          <p className="text-sm capitalize font-medium">
            {service?.serviceData?.serviceDetails?.requiredDocuments}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">
            {translate("availableTimeSlot")}:
          </p>
          <p className="text-sm capitalize font-medium">
            {service?.serviceData?.serviceDetails?.availabilityStart}-
            {service?.serviceData?.serviceDetails?.availabilityEnd}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="font-semibold text-sm">{translate("description")}:</p>
          <p className="text-sm font-medium">
            {service?.serviceData?.description}
          </p>
        </div>
      </div>
      <Separator className="mt-2" />
      <div className="flex flex-col gap-1 mt-2">
        <p className="text-sm font-semibold">{translate("serviceDate")}:</p>
        <Calendar
          mode="single"
          locale={calendarLanguage.secondValue}
          formatters={{
            formatMonthDropdown: (date) => monthFormatter.format(date),
          }}
          disabled={{ before: new Date() }}
          defaultMonth={createLocalDate(formData.serviceDate)}
          startMonth={new Date(new Date().getFullYear(), new Date().getMonth())}
          endMonth={new Date(2050, 11)}
          required
          selected={createLocalDate(formData.serviceDate)}
          onSelect={(selectedDate) => {
            const adjustedStartDate = adjustDateToTimezone(selectedDate);
            const dateToString = adjustedStartDate?.toISOString().split("T")[0];
            setFormData((prev: any) => ({
              ...prev,
              serviceDate: dateToString,
            }));
          }}
          className="rounded-md border shadow-sm w-full max-w-2xl"
          classNames={{
            today: "bg-transparent text-foreground rounded-md",
          }}
          {...({ disableAutoUnselect: true } as any)}
          captionLayout="dropdown"
        />
        <p className="text-red-500 text-[12px] mt-1">
          {isButtonDisabled.errorText}
        </p>
      </div>
      { petOwnerPetData?.length > 0 && (
        <>
          <p className="text-sm font-semibold">{translate("petSelection")}</p>
          <Select onValueChange={(selected) => setSelectedPet(selected)}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder={translate("chooseAPet")} />
            </SelectTrigger>
            <SelectContent>
              {petOwnerPetData?.map((pet: any) => {
                return (
                  <SelectItem key={pet?._id} value={pet?._id}>
                    {pet?.name}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </>
      )}
      <div className="flex flex-col gap-1 max-w-2xl">
        <p className="text-sm font-semibold">{translate("note")}:</p>
        <Textarea name="note" value={formData.note} onChange={handleChange} />
      </div>
      <div className="flex items-center gap-2">
        <p className="max-sm:text-sm font-medium">{translate("price")}:</p>
        <p className="font-bold">
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(service?.serviceData?.total)) + "₺"}
        </p>
      </div>

      <div className="flex justify-end gap-5 pb-1">
        <ButtonSecondary
          className="w-1/2 sm:w-32"
          type="button"
          onClick={closeModal}
        >
          Vazgeç
        </ButtonSecondary>
        <ButtonPrimary
          disabled={disabled || isButtonDisabled.disabled}
          type="submit"
          className="w-1/2 sm:w-32"
        >
          {loading ? <LoadingSpinner /> : translate("add")}
        </ButtonPrimary>
      </div>
    </div>
  );
};

export default VeterinaryService;
