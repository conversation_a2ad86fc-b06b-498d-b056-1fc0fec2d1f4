"use client";
import React, { useState } from "react";
import RoomSelectStep from "./(steps)/(roomSelectStep)/roomSelectStep";

interface AtDoorReservationContainerProps {
  hotelToken: string | undefined;
  hotelData: any;
}

const AtDoorReservationContainer: React.FC<AtDoorReservationContainerProps> = ({
  hotelToken,
  hotelData,
}) => {
  const [step, setStep] = useState<number>(1);

  return <div className="container">{step === 1 && <RoomSelectStep hotelToken={hotelToken} hotelData={hotelData} />}</div>;
};

export default AtDoorReservationContainer;
