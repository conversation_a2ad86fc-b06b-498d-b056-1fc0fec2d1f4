"use client";
import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import VeterinaryService from "./(Services)/VeterinaryService";
// import TransportationService from "./(Services)/TransportationService";
// import GroomingService from "./(Services)/GroomingService";

interface ServiceFormProps {
  hotelToken: string | undefined;
  serviceData: any;
}

const ServiceForm: React.FC<ServiceFormProps> = ({
  hotelToken,
  serviceData,
}) => {
  const [selectedService, setSelectedService] = useState<any>(null);
  const [selectedServiceType, setSelectedServiceType] = useState<string>("");

  console.log(serviceData);

  const handleServiceSelection = (serviceId: string) => {
    const service = serviceData.find((s: any) => s._id === serviceId);
    if (service) {
      setSelectedServiceType(service.serviceType);
      setSelectedService(service);
    }
  };

  return (
    <div className="listingSection__wrap_disable space-y-3">
      <Select
        value={selectedService?._id || ""}
        onValueChange={(selected) => handleServiceSelection(selected)}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Hizmet Seç" />
        </SelectTrigger>
        <SelectContent>
          {serviceData?.map((service: any) => (
            <SelectItem key={service._id} value={service._id}>
              {service.serviceData.serviceName} -{" "}
              <span className="font-medium text-neutral-700 dark:text-neutral-300">
                {service?.serviceType === "veterinaryServices"
                  ? "Veteriner"
                  : service?.serviceType === "transportationServices"
                    ? "Pet Ulaşım"
                    : service?.serviceType === "groomingServices"
                      ? "Pet Kuaför"
                      : ""}
              </span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {selectedServiceType === "veterinaryServices" && <p> Veteriner seçili</p>}
      {selectedServiceType === "transportationServices" && (
        <p> Pet Ulaşım seçili</p>
      )}
      {selectedServiceType === "groomingServices" && <p> Pet Kuaför seçili</p>}
      {/* {selectedServiceType === "veterinaryService" && (
        <VeterinaryService service={serviceData} hotelToken={hotelToken} />
      )} */}
      {/* {selectedServiceType === "transportationService" && (
        <TransportationService
          hotelToken={hotelToken}
          closeModal={closeModal}
        />
      )}
      {selectedServiceType === "groomingService" && (
        <GroomingService hotelToken={hotelToken} closeModal={closeModal} />
      )} */}
    </div>
  );
};

export default ServiceForm;
