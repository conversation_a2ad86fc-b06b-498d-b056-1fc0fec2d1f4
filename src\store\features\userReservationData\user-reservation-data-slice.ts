import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type {
  userReservationDataTypes,
  userReservationDataStates,
} from "./user-reservation-data-types";

const initialStartDate = new Date(
  new Date().getTime() + 7 * 24 * 60 * 60 * 1000
)
  .toISOString()
  .split("T")[0];
const initialEndDate = new Date(new Date().getTime() + 14 * 24 * 60 * 60 * 1000)
  .toISOString()
  .split("T")[0];

const initialState: userReservationDataStates = {
  userReservationData: {
    selectedRooms: [],
    roomId: [],
    hotelId: "",
    subscriptionId: [],
    serviceId: [],
    selectedPet: null,
    selectedPetKind: null,
    selectedPetName: null,
    startDate: initialStartDate,
    endDate: initialEndDate,
  },
};

const selectedRoomsSlice = createSlice({
  name: "userReservationData",
  initialState,
  reducers: {
    setUserReservationData: (
      state,
      action: PayloadAction<userReservationDataTypes>
    ) => {
      state.userReservationData = action.payload;
    },
  },
});

export const { setUserReservationData } = selectedRoomsSlice.actions;
export default selectedRoomsSlice.reducer;
