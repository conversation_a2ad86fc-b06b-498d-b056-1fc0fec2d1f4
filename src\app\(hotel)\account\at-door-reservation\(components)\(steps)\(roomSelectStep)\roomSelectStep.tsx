"use client";
import React, { useState } from "react";
import SideBar from "../../sideBar";
import DatePickerSection from "./(components)/DatePickerSection";
import ActionButtons from "./(components)/ActionButtons";
import ReservationDialog from "./(components)/ReservationDialog";

interface RoomSelectStepProps {
  hotelToken: string | undefined;
  hotelData: any;
}

const RoomSelectStep: React.FC<RoomSelectStepProps> = ({
  hotelToken,
  hotelData,
}) => {
  const [startDate, setStartDate] = useState<Date | string | undefined>(
    undefined
  );
  const [endDate, setEndDate] = useState<Date | string | undefined>(undefined);
  const [selectedPetTypes, setSelectedPetTypes] = useState<string | null>(null);
  const [selectedRooms, setSelectedRooms] = useState<string | null>(null);
  const [allocations, setAllocations] = useState<any[]>([[]]);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeSection, setActiveSection] = useState<
    "reservation" | "service" | "subscription" | null
  >(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleDialogClose = () => {
    setDialogOpen(false);
    setActiveSection(null);
    setSelectedPetTypes(null);
    setSelectedRooms(null);
  };

  const handleButtonClick = (
    section: "reservation" | "service" | "subscription"
  ) => {
    setActiveSection(section);
    setDialogOpen(true);
  };

  const onChangeDate = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start ?? undefined);
    setEndDate(end ?? undefined);
  };

  return (
    <div className="flex flex-row gap-4">
      <div className="flex flex-col items-center gap-10 md:gap-4 w-full lg:w-3/5 lg:pr-10 xl:w-2/3">
        <DatePickerSection
          startDate={startDate}
          endDate={endDate}
          onChangeDate={onChangeDate}
        />

        <ActionButtons
          startDate={startDate}
          endDate={endDate}
          onButtonClick={handleButtonClick}
        />

        <ReservationDialog
          dialogOpen={dialogOpen}
          activeSection={activeSection}
          startDate={startDate}
          endDate={endDate}
          selectedPetTypes={selectedPetTypes}
          selectedRooms={selectedRooms}
          allocations={allocations}
          loading={loading}
          hotelToken={hotelToken}
          hotelData={hotelData}
          onDialogClose={handleDialogClose}
          onPetTypeChange={setSelectedPetTypes}
          onRoomChange={setSelectedRooms}
          onAllocationsChange={setAllocations}
          onLoadingChange={setLoading}
        />
      </div>
      <div className="mt-14 hidden grow lg:mt-0 lg:block">
        <div className="sticky top-28">
          <SideBar />
        </div>
      </div>
    </div>
  );
};

export default RoomSelectStep;
