import { configureStore } from "@reduxjs/toolkit";
import hotelAuthReducer from "./features/hotelAuth/hotel-auth-slice";
import userReservationDataReducer from "./features/userReservationData/user-reservation-data-slice";
import hotelCalendarReducer from "./features/hotelCalendar/calendar-slice";
import calculatedRoomDataReducer from "./features/calculatedRoomData/calculated-room-data-slice";

const store = configureStore({
  reducer: {
    hotelAuth: hotelAuthReducer,
    userReservationData: userReservationDataReducer,
    hotelCalendar: hotelCalendarReducer,
    calculatedRoomData: calculatedRoomDataReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
