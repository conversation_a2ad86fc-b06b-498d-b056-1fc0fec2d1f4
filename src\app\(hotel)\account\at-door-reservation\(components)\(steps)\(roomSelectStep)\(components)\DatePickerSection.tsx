"use client";
import React from "react";
import DatePicker from "react-datepicker";
import { createLocalDate } from "@/utils/createLocalDate";
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth";
import DatePickerCustomDay from "@/components/DatePickerCustomDay";
import { registerLocale } from "react-datepicker";
import { tr } from "date-fns/locale";

registerLocale("tr", tr);

interface DatePickerSectionProps {
  startDate: Date | string | undefined;
  endDate: Date | string | undefined;
  onChangeDate: (dates: [Date | null, Date | null]) => void;
}

const DatePickerSection: React.FC<DatePickerSectionProps> = ({
  startDate,
  endDate,
  onChangeDate,
}) => {
  return (
    <div className="flex flex-col items-center gap-10 md:gap-4">
      <div className="rounded-3xl bg-white max-md:w-[95vw] max-md:max-w-md p-6 max-md:p-4 shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-neutral-800">
        <DatePicker
          locale="tr"
          selected={startDate ? createLocalDate(startDate) : undefined}
          minDate={new Date()}
          onChange={onChangeDate}
          startDate={startDate ? createLocalDate(startDate) : undefined}
          endDate={endDate ? createLocalDate(endDate) : undefined}
          selectsRange
          monthsShown={2}
          showPopperArrow={false}
          inline
          renderCustomHeader={(p) => (
            <DatePickerCustomHeaderTwoMonth {...p} />
          )}
          renderDayContents={(day, date) => (
            <DatePickerCustomDay dayOfMonth={day} date={date} />
          )}
        />
      </div>
      <div>
        {!startDate && !endDate ? (
          <div className="text-red-500">Lütfen tarih seçiniz.</div>
        ) : startDate && !endDate ? (
          <div className="text-red-500">
            Lütfen bitiş tarihini seçiniz.
          </div>
        ) : !startDate && endDate ? (
          <div className="text-red-500">
            Lütfen başlangıç tarihini seçiniz.
          </div>
        ) : (
          <div className="text-neutral-500">
            {new Intl.DateTimeFormat("tr-TR", {
              day: "2-digit",
              month: "long",
              year: "numeric",
            }).format(new Date(startDate as string))}{" "}
            -{" "}
            {new Intl.DateTimeFormat("tr-TR", {
              day: "2-digit",
              month: "long",
              year: "numeric",
            }).format(new Date(endDate as string))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DatePickerSection;
