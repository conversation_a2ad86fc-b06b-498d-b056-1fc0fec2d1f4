"use client";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import ReservationForm from "./ReservationForm";
import ServiceForm from "./ServiceForm";
import SubscriptionForm from "./SubscriptionForm";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";

interface ReservationDialogProps {
  startDate: Date | string | undefined;
  endDate: Date | string | undefined;
  hotelToken: string | undefined;
  hotelData: any;
  subscriptionData: any;
  serviceData: any;
}

const ReservationDialog: React.FC<ReservationDialogProps> = ({
  startDate,
  endDate,
  hotelToken,
  hotelData,
  subscriptionData,
  serviceData,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeSection, setActiveSection] = useState<
    "reservation" | "service" | "subscription" | null
  >(null);
  const [selectedPetTypes, setSelectedPetTypes] = useState<string | null>(null);
  const [selectedRooms, setSelectedRooms] = useState<string | null>(null);
  const [allocations, setAllocations] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const { addItemToCart } = useHotelAtDoorReservation();

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const handleDialogClose = () => {
    setDialogOpen(false);
    setActiveSection(null);
    setSelectedPetTypes(null);
    setSelectedRooms(null);
  };

  const handleButtonClick = (
    section: "reservation" | "service" | "subscription"
  ) => {
    setActiveSection(section);
    setDialogOpen(true);
  };

  const handleAddToCart = () => {
    switch (activeSection) {
      case "reservation":
        const reservationRequestBody = {
          itemType: "reservation",
          itemData: {
            room: selectedRooms,
            startDate: startDate,
            endDate: endDate,
            pet: "",
          },
          selectedItems: calculatedRoomData?.selectedItems || [],
          totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
        };

        try {
          addItemToCart(hotelToken, reservationRequestBody);
          console.log("Ekleme işlemi başarılı:", reservationRequestBody);
          handleDialogClose();
        } catch (error) {
          console.error("Ekleme işlemi başarısız:", error);
        }
        break;
      case "service":
        const serviceRequestBody = { 
          itemType: "service",
          itemData: {
            ...service?.serviceData,
            note: formData.note,
            serviceDate: formData.serviceDate,
            serviceType: service?.serviceType,
            serviceHotelId: service?._id,
            hotel: hotelId,
            pet: "",
          },
          selectedItems: calculatedRoomData?.selectedItems || [],
          totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
        };

        addItemToCart(
          hotelToken,
          serviceRequestBody,
        );
        break;
      case "subscription":
        console.log("Üyelik kartı için alan doldurma işlemi");
        handleDialogClose();
        break;
    }
  };

  const renderDialogContent = () => {
    switch (activeSection) {
      case "reservation":
        return (
          <ReservationForm
            startDate={startDate}
            endDate={endDate}
            selectedPetTypes={selectedPetTypes}
            selectedRooms={selectedRooms}
            hotelToken={hotelToken}
            hotelData={hotelData}
            onPetTypeChange={setSelectedPetTypes}
            onRoomChange={setSelectedRooms}
            onAllocationsChange={setAllocations}
            onLoadingChange={setLoading}
          />
        );
      case "service":
        return (
          <ServiceForm hotelToken={hotelToken} serviceData={serviceData} />
        );
      case "subscription":
        return <SubscriptionForm />;
      default:
        return (
          <div className="text-neutral-500">Lütfen bir seçenek belirleyin.</div>
        );
    }
  };

  return (
    <>
      {/* Action Buttons */}
      <div className="flex flex-col md:flex-row gap-5 w-full max-md:w-[95vw] max-md:max-w-md pt-2 pb-4 px-10 justify-center">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
          onClick={() => handleButtonClick("reservation")}
          disabled={!startDate || !endDate}
        >
          Yeni Konaklama Ekle
        </Button>
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
          onClick={() => handleButtonClick("service")}
          disabled={!startDate || !endDate}
        >
          Yeni Hizmet Ekle
        </Button>
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
          onClick={() => handleButtonClick("subscription")}
          disabled={!startDate || !endDate}
        >
          Yeni Üyelik Kartı Ekle
        </Button>
      </div>

      {/* Dialog */}
      <Dialog open={dialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {activeSection === "reservation" && "Konaklama"}
              {activeSection === "service" && "Hizmet"}
              {activeSection === "subscription" && "Üyelik Kartı"}
            </DialogTitle>
            <DialogDescription>
              Lütfen aşağıdaki alanlardan seçim yapınız.
            </DialogDescription>
          </DialogHeader>
          {renderDialogContent()}
          <DialogFooter>
            <Button variant="ghost" onClick={handleDialogClose}>
              İptal
            </Button>
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              onClick={handleAddToCart}
            >
              Ekle
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ReservationDialog;
