"use client";
import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { petTypes } from "@/types/petOwner/petTypes";

interface ReservationFormProps {
  startDate: Date | string | undefined;
  endDate: Date | string | undefined;
  selectedPetTypes: string | null;
  selectedRooms: string | null;
  allocations: any[];
  loading: boolean;
  onPetTypeChange: (value: string) => void;
  onRoomChange: (value: string) => void;
}

const ReservationForm: React.FC<ReservationFormProps> = ({
  startDate,
  endDate,
  selectedPetTypes,
  selectedRooms,
  allocations,
  loading,
  onPetTypeChange,
  onRoomChange,
}) => {
  const petTypeList = petTypes.map((petType) => ({
    value: petType.value,
    label: petType.label,
  }));

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-2 w-96">
        <div>
          <Label>Pet Türü</Label>
          <Select
            onValueChange={onPetTypeChange}
            disabled={!startDate || !endDate || loading}
          >
            <SelectTrigger className="rounded-md border border-neutral-200 dark:border-neutral-700 min-h-10 w-56 mt-1">
              <SelectValue placeholder="Pet türü seçin" />
            </SelectTrigger>
            <SelectContent>
              {petTypeList.map((petType) => (
                <SelectItem key={petType.value} value={petType.value}>
                  {petType.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Oda Seçimi</Label>
          <Select
            disabled={
              !startDate || !endDate || !selectedPetTypes || loading
            }
            onValueChange={onRoomChange}
          >
            <SelectTrigger className="rounded-md border border-neutral-200 dark:border-neutral-700 min-h-10 w-56 mt-1">
              <SelectValue placeholder="Lütfen oda seçiniz" />
            </SelectTrigger>
            <SelectContent>
              {allocations?.length > 0 ? (
                allocations?.map((room: any, idx: number) => (
                  <SelectItem
                    key={idx}
                    value={room?.firstAvailableRoom?._id}
                  >
                    <span className="font-semibold">
                      {room?.roomGroup?.roomGroupName}
                    </span>{" "}
                    -{" "}
                    {new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(room?.avgRoomPrice)) + "₺"}{" "}
                    (
                    <span className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
                      gecelik fiyatı
                    </span>
                    )
                  </SelectItem>
                ))
              ) : (
                <div className="text-center p-2 text-neutral-400 dark:text-neutral-500">
                  Uygun oda bulunamadı
                </div>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default ReservationForm;
