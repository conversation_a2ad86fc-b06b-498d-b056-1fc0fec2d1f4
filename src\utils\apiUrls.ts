type Paths = Record<string, string>;

const NEXT_PUBLIC_API_URI = process.env.NEXT_PUBLIC_API_URI!;

const generateLink = (baseUrl: string, paths: Paths): Paths => {
  const keys = Object.keys(paths);
  const newPaths: Paths = {};

  keys.forEach((key) => {
    newPaths[key] = `${baseUrl}${paths[key]}`;
  });

  return newPaths;
};

export const ADMIN_API_PATHS = generateLink(`${NEXT_PUBLIC_API_URI}/admin`, {
  dashboard: "",
});

export const HOTEL_API_PATHS = generateLink(`${NEXT_PUBLIC_API_URI}/hotel`, {
  myHotel: "/account/myHotel",
  getUpdateActionRequired: "/account/myHotel/updateActionRequired",
  reservations: "/account/reservations",
  users: "/account/users",
  policy: "/account/policy",
  rooms: "/account/rooms/getRoomGroups",
  roomGroupsWithAllocation: "/account/rooms/getRoomGroupsWithAllocation",
  hotelLanding: "/account/landingPage",
  addOrderDetail: "/account/payment/addOrderDetail",
  subMerchant: "/account/hotelSubMerchant/getSubMerchant",
  todayReservations: "/account/review/reservationStats",
  todayRevenue: "/account/review/todayRevenue",
  todayTotalGuest: "/account/review/guestStats",
  todayCancelled: "/account/review/cancelledReservations",
  updateReservationStatus: "/account/reservations/updateStatus",
  hotelAnalytics: "/report/hotelAnalytics",
  updateHotelStatus: "/account/hotelStatus/updateHotelStatus",
  addTransaction: "/account/balances/addTransaction",
  removeTransaction: "/account/balances/removeTransaction",
  checkHotelSubMerchant: "/account/hotelSubMerchant/checkSubMerchantExists",
  addAdditionalService: "/account/services/addAvailableService",
  getAdditionalServices: "/account/services/getAvailableServices",
  deleteAdditionalService: "/account/services/removeAvailableService",
  updateAdditionalService: "/account/services/updateAvailableService",
  getHotelsSubscribers: "/account/subscriptions/getHotelsSubscribers",
  getAllServices: "/account/services/getAllServices",
  getSubscriptions: "/account/subscriptions/getAvailableSubscriptions",
  addSubscription: "/account/subscriptions/describeSubscription",
  updateSubscription: "/account/subscriptions/updateSubscription",
  deleteSubscription: "/account/subscriptions/removeSubscription",
  todayPayment: "/report/todayDepositAmount",
  tomorrowPayment: "/report/tomorrowDepositAmount",
  getHotelCustomerByPhone: "/account/hotelCustomers/getHotelCustomerByPhone",
  getAllHotelCustomers: "/account/hotelCustomers/getAllHotelCustomers",
  getHotelPetByOwner: "/account/hotelPets/getHotelPetByOwner",
  addHotelCustomer: "/account/hotelCustomers/addHotelCustomer",
  updateHotelCustomer: "/account/hotelCustomers/updateHotelCustomer",
  removeHotelCustomer: "/account/hotelCustomers/removeHotelCustomer",
  addHotelPet: "/account/hotelPets/addHotelPet",
  updateHotelPet: "/account/hotelPets/updateHotelPet",
  removeHotelPet: "/account/hotelPets/removeHotelPet",
  getCalculateReservation: "/account/atDoorReservations/calculate",
  getReserve: "/account/atDoorReservations/reserve",
  getMembershipByHotel: "/account/features/getMembershipByHotel",
  getOrdersBetweenDates: "/report/financeReports/getOrdersBetweenDates",
  getRevenueAnalytic: "/report/financeReports/getRevenueAnalytic",
  getPaymentTypes: "/report/financeReports/getPaymentTypes",
  getDepositAmounts: "/report/financeReports/getDepositAmounts",
  getCancelledReservationStats:
    "/report/financeReports/getCancelledReservationStats",
  getBalanceByOrder: "/account/balances/getBalanceByOrder",
  getBalanceByReservation: "/account/balances/getBalanceByReservation",
  receivePayment: "/account/subscriptionPayment/receivePayment",
  addNewCard: "/account/subscriptionPayment/addNewCard",
  setPrimaryCard: "/account/subscriptionPayment/setPrimaryCard",
  removeSavedCard: "/account/subscriptionPayment/removeSavedCard",
  unsubscribeHotel: "/account/subscriptionPayment/unsubscribeHotel",
  checkCardValidation: "/account/subscriptionPayment/checkCardValidation",
  getHotelNextPaymentDate:
    "/account/subscriptionPayment/getHotelNextPaymentDate",
  getHotelCards: "/account/subscriptionPayment/getHotelCards",
  getHotelSubscriptionStatus:
    "/account/subscriptionPayment/getHotelSubscriptionStatus",
  getPromos: "/account/discounts/getPromos",
  createPromo: "/account/discounts/createPromo",
  updatePromo: "/account/discounts/updatePromo",
  removePromo: "/account/discounts/removePromo",
  getInstallment: "/account/installment/getInstallmentCampaigns",
  defineInstallment: "/account/installment/defineInstallmentCampaign",
  updateInstallment: "/account/installment/updateInstallmentCampaign",
  deleteInstallment: "/account/installment/deleteInstallmentCampaign",
  getUnseenWebPushes: "/account/notification/getUnseenWebPushes",
  updateWebPush: "/account/notification/updateWebPush",
  getOrderById: "/account/atDoorReservations/getOrderById",
  addItemToCart: "/account/atDoorReservations/addItemToCart",
  removeItemFromCart: "/account/atDoorReservations/remove",
  createOrder: "/account/atDoorReservations/createOrder",
  finishOrder: "/account/atDoorReservations/completeOrder",
  addItemToOrder: "/account/atDoorReservations/addItemToOrder",
  removeItemFromOrder: "/account/atDoorReservations/removeItemFromOrder",
  updateOrder: "/account/atDoorReservations/updateOrder",
});

export const S3_API_PATHS = generateLink(`${NEXT_PUBLIC_API_URI}/s3`, {
  imageUpload: "/imageUpload",
  fileUpload: "/fileUpload",
});

export const PUBLIC_API_PATHS = generateLink(`${NEXT_PUBLIC_API_URI}/pub`, {
  roomAllocationsGetList: "/roomAllocations/getList",
});
