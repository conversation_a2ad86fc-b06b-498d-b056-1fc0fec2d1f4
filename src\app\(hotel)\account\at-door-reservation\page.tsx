import React from "react";
import { cookies } from "next/headers";
import AtDoorReservationsContainer from "./(components)/atDoorReservationsContainer";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";

const AtDoorReservationPage = async ({
  searchParams,
}: {
  searchParams: Record<string, string | string[] | undefined>;
}) => {
  const step = searchParams.step || 1;
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();

  return (
    <div>
      <AtDoorReservationsContainer hotelToken={hotelToken} hotelData={hotelData?.data} />
    </div>
  );
};

export default AtDoorReservationPage;
