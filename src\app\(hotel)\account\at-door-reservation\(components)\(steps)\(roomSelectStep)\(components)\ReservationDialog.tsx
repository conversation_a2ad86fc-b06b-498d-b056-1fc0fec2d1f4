"use client";
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import ReservationForm from "./ReservationForm";
import ServiceForm from "./ServiceForm";
import SubscriptionForm from "./SubscriptionForm";

interface ReservationDialogProps {
  dialogOpen: boolean;
  activeSection: "reservation" | "service" | "subscription" | null;
  startDate: Date | string | undefined;
  endDate: Date | string | undefined;
  selectedPetTypes: string | null;
  selectedRooms: string | null;
  allocations: any[];
  loading: boolean;
  onDialogClose: () => void;
  onPetTypeChange: (value: string) => void;
  onRoomChange: (value: string) => void;
  onAddToCart: () => void;
}

const ReservationDialog: React.FC<ReservationDialogProps> = ({
  dialogOpen,
  activeSection,
  startDate,
  endDate,
  selectedPetTypes,
  selectedRooms,
  allocations,
  loading,
  onDialogClose,
  onPetTypeChange,
  onRoomChange,
  onAddToCart,
}) => {
  const renderDialogContent = () => {
    switch (activeSection) {
      case "reservation":
        return (
          <ReservationForm
            startDate={startDate}
            endDate={endDate}
            selectedPetTypes={selectedPetTypes}
            selectedRooms={selectedRooms}
            allocations={allocations}
            loading={loading}
            onPetTypeChange={onPetTypeChange}
            onRoomChange={onRoomChange}
          />
        );
      case "service":
        return <ServiceForm />;
      case "subscription":
        return <SubscriptionForm />;
      default:
        return (
          <div className="text-neutral-500">Lütfen bir seçenek belirleyin.</div>
        );
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={onDialogClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {activeSection === "reservation" && "Konaklama"}
            {activeSection === "service" && "Hizmet"}
            {activeSection === "subscription" && "Üyelik Kartı"}
          </DialogTitle>
          <DialogDescription>
            Lütfen aşağıdaki alanlardan seçim yapınız.
          </DialogDescription>
        </DialogHeader>
        {renderDialogContent()}
        <DialogFooter>
          <Button variant="ghost" onClick={onDialogClose}>
            İptal
          </Button>
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={onAddToCart}
          >
            Ekle
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReservationDialog;
