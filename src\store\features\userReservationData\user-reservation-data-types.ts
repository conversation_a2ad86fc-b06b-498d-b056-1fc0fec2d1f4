export interface userReservationDataTypes {
  selectedRooms: {
    hotelName: null | string;
    roomName: null | string;
    roomImage: any;
    price: null | number;
    roomId: null | string;
  }[];
  roomId: string[];
  hotelId: string;
  subscriptionId: string[];
  serviceId: string[];
  selectedPet: null | string;
  selectedPetKind: null | string;
  selectedPetName: null | string;
  startDate: string | Date | undefined;
  endDate: string | Date | undefined;
}

export interface userReservationDataStates {
  userReservationData: userReservationDataTypes;
}
