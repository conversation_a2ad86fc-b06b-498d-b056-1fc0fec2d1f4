"use client";
import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import Input from "@/shared/Input";
import { petTypes } from "@/types/petOwner/petTypes";
import { Label } from "@/components/ui/label";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import { getSelectedHotelAllocation } from "@/actions/(protected)/pub/getSelectedHotelAllocation";
import DatePicker from "react-datepicker";
import { createLocalDate } from "@/utils/createLocalDate";
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth";
import DatePickerCustomDay from "@/components/DatePickerCustomDay";
import { registerLocale } from "react-datepicker";
import { tr } from "date-fns/locale";
import SideBar from "../../sideBar";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";

interface RoomSelectStepProps {
  hotelToken: string | undefined;
  hotelData: any;
}

const RoomSelectStep: React.FC<RoomSelectStepProps> = ({
  hotelToken,
  hotelData,
}) => {
  const { addItemToCart } = useHotelAtDoorReservation();
  const userReservationData = useSelector(
    (state: RootState) => state.userReservationData.userReservationData
  );
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  registerLocale("tr", tr);
  const [startDate, setStartDate] = useState<Date | string | undefined>(
    undefined
  );
  const [endDate, setEndDate] = useState<Date | string | undefined>(undefined);
  const [selectedPetTypes, setSelectedPetTypes] = useState<string | null>(null);
  const [selectedRooms, setSelectedRooms] = useState<string | null>(null);
  const [allocations, setAllocations] = useState<any[]>([[]]);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeSection, setActiveSection] = useState<
    "reservation" | "service" | "subscription" | null
  >(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleDialogClose = () => {
    setDialogOpen(false);
    setActiveSection(null);
    setSelectedPetTypes(null);
    setSelectedRooms(null);
  };

  const handleButtonClick = (
    section: "reservation" | "service" | "subscription"
  ) => {
    setActiveSection(section);
    setDialogOpen(true);
  };

  const renderDialogContent = () => {
    switch (activeSection) {
      case "reservation":
        return (
          <div className="flex flex-col gap-2">
            <div className="flex flex-col gap-2 w-96">
              <div>
                <Label>Pet Türü</Label>
                <Select
                  onValueChange={(value) => {
                    setSelectedPetTypes(value);
                  }}
                  disabled={!startDate || !endDate || loading}
                >
                  <SelectTrigger className="rounded-md border border-neutral-200 dark:border-neutral-700 min-h-10 w-56 mt-1">
                    <SelectValue placeholder="Pet türü seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {petTypeList.map((petType) => (
                      <SelectItem key={petType.value} value={petType.value}>
                        {petType.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Oda Seçimi</Label>
                <Select
                  disabled={
                    !startDate || !endDate || !selectedPetTypes || loading
                  }
                  onValueChange={(value) => {
                    setSelectedRooms(value);
                  }}
                >
                  <SelectTrigger className="rounded-md border border-neutral-200 dark:border-neutral-700 min-h-10 w-56 mt-1">
                    <SelectValue placeholder="Lütfen oda seçiniz" />
                  </SelectTrigger>
                  <SelectContent>
                    {allocations?.length > 0 ? (
                      allocations?.map((room: any, idx: number) => (
                        <SelectItem
                          key={idx}
                          value={room?.firstAvailableRoom?._id}
                        >
                          <span className="font-semibold">
                            {room?.roomGroup?.roomGroupName}
                          </span>{" "}
                          -{" "}
                          {new Intl.NumberFormat("tr-TR", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }).format(Number(room?.avgRoomPrice)) + "₺"}{" "}
                          (
                          <span className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
                            gecelik fiyatı
                          </span>
                          )
                        </SelectItem>
                      ))
                    ) : (
                      <div className="text-center p-2 text-neutral-400 dark:text-neutral-500">
                        Uygun oda bulunamadı
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        );
      case "service":
        return (
          <div className="flex flex-col gap-2">
            <Label>Hizmet Detayları</Label>
            {/* Hizmet ile ilgili inputlar */}
            <Input placeholder="Hizmet açıklaması" className="w-96" />
          </div>
        );
      case "subscription":
        return (
          <div className="flex flex-col gap-2">
            <Label>Üyelik Kartı Bilgileri</Label>
            {/* Üyelik kartı ile ilgili inputlar */}
            <Input placeholder="Kart Numarası" className="w-96" />
          </div>
        );
      default:
        return (
          <div className="text-neutral-500">Lütfen bir seçenek belirleyin.</div>
        );
    }
  };

  const onChangeDate = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start ?? undefined);
    setEndDate(end ?? undefined);
  };

  const fetchAllocations = async () => {
    if (!startDate || !endDate || !selectedPetTypes || !hotelToken) return;

    setLoading(true);

    const adjustedStartDate =
      adjustDateToTimezone(startDate)?.toISOString().split("T")[0] || "";
    const adjustedEndDate =
      adjustDateToTimezone(endDate)?.toISOString().split("T")[0] || "";

    try {
      const result = await getSelectedHotelAllocation(
        hotelData?._id,
        selectedPetTypes,
        adjustedStartDate,
        adjustedEndDate
      );
      console.log(result);
      setAllocations(result?.data?.allocationData);
    } catch (error) {
      console.error("Failed to fetch allocations:", error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchAllocations();
    console.log(allocations);
  }, [startDate, endDate, selectedPetTypes]);

  const petTypeList = petTypes.map((petType) => ({
    value: petType.value,
    label: petType.label,
  }));

  const addToCartHandler = () => {
    if (!activeSection) return;

    if (activeSection === "reservation") {
      const requestBody = {
        itemType: "reservation",
        itemData: {
          room: selectedRooms,
          startDate: startDate,
          endDate: endDate,
          pet: userReservationData?.selectedPet,
        },
        selectedItems: calculatedRoomData?.selectedItems || [],
        totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
      };

      try {
        addItemToCart(hotelToken, requestBody);
        console.log("Ekleme işlemi başarılı:", requestBody);
        handleDialogClose(); // Dialog'u kapat
      } catch (error) {
        console.error("Ekleme işlemi başarısız:", error);
      }
    }

    if (activeSection === "service") {
      console.log("Hizmet için alan doldurma işlemi");
      // Boş bırakılmış bir alan. İleride doldurulacak.
    }

    if (activeSection === "subscription") {
      console.log("Üyelik kartı için alan doldurma işlemi");
      // Boş bırakılmış bir alan. İleride doldurulacak.
    }

    handleDialogClose();
  };

  return (
    <div className="flex flex-row gap-4">
      <div className="flex flex-col items-center gap-10 md:gap-4 w-full lg:w-3/5 lg:pr-10 xl:w-2/3">
        <div className="rounded-3xl bg-white max-md:w-[95vw] max-md:max-w-md p-6 max-md:p-4 shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-neutral-800">
          <DatePicker
            locale="tr"
            selected={startDate ? createLocalDate(startDate) : undefined}
            minDate={new Date()}
            onChange={onChangeDate}
            startDate={startDate ? createLocalDate(startDate) : undefined}
            endDate={endDate ? createLocalDate(endDate) : undefined}
            selectsRange
            monthsShown={2}
            showPopperArrow={false}
            inline
            renderCustomHeader={(p) => (
              <DatePickerCustomHeaderTwoMonth {...p} />
            )}
            renderDayContents={(day, date) => (
              <DatePickerCustomDay dayOfMonth={day} date={date} />
            )}
          />
        </div>
        <div>
          {!startDate && !endDate ? (
            <div className="text-red-500">Lütfen tarih seçiniz.</div>
          ) : startDate && !endDate ? (
            <div className="text-red-500">
              Lütfen bitiş tarihini seçiniz.
            </div>
          ) : !startDate && endDate ? (
            <div className="text-red-500">
              Lütfen başlangıç tarihini seçiniz.
            </div>
          ) : (
            <div className="text-neutral-500">
              {new Intl.DateTimeFormat("tr-TR", {
                day: "2-digit",
                month: "long",
                year: "numeric",
              }).format(new Date(startDate as string))}{" "}
              -{" "}
              {new Intl.DateTimeFormat("tr-TR", {
                day: "2-digit",
                month: "long",
                year: "numeric",
              }).format(new Date(endDate as string))}
            </div>
          )}
        </div>
        <div className="flex flex-col md:flex-row gap-5 w-full max-md:w-[95vw] max-md:max-w-md pt-2 pb-4 px-10 justify-center">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
            onClick={() => handleButtonClick("reservation")}
            disabled={!startDate || !endDate}
          >
            Yeni Konaklama Ekle
          </Button>
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
            onClick={() => handleButtonClick("service")}
            disabled={!startDate || !endDate}
          >
            Yeni Hizmet Ekle
          </Button>
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
            onClick={() => handleButtonClick("subscription")}
            disabled={!startDate || !endDate}
          >
            Yeni Üyelik Kartı Ekle
          </Button>
        </div>
        <Dialog open={dialogOpen} onOpenChange={handleDialogClose}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {activeSection === "reservation" && "Konaklama"}
                {activeSection === "service" && "Hizmet"}
                {activeSection === "subscription" && "Üyelik Kartı"}
              </DialogTitle>
              <DialogDescription>
                Lütfen aşağıdaki alanları doldurun.
              </DialogDescription>
            </DialogHeader>
            {renderDialogContent()}
            <DialogFooter>
              <Button variant="ghost" onClick={handleDialogClose}>
                İptal
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                onClick={addToCartHandler}
              >
                Ekle
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      <div className="mt-14 hidden grow lg:mt-0 lg:block">
        <div className="sticky top-28">
          <SideBar />
        </div>
      </div>
    </div>
  );
};

export default RoomSelectStep;
